import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';


import '../../Cattle/models/cattle_isar.dart';
import '../controllers/health_details_controller.dart';

import '../../../constants/app_layout.dart';
import '../../../constants/app_tabs.dart';



import 'health_details_analytics_tab.dart';
import 'health_details_records_tab.dart';

class HealthDetailsScreen extends StatefulWidget {
  final CattleIsar cattle;
  final Function(CattleIsar)? onCattleUpdated;

  const HealthDetailsScreen({
    Key? key,
    required this.cattle,
    this.onCattleUpdated,
  }) : super(key: key);

  @override
  State<HealthDetailsScreen> createState() => _HealthDetailsScreenState();
}

class _HealthDetailsScreenState extends State<HealthDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Initialize the controller with the cattle data
    final controller = GetIt.instance<HealthDetailsController>();
    controller.initialize(widget.cattle);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<HealthDetailsController>(
      create: (_) => GetIt.instance<HealthDetailsController>(),
      child: UniversalLayout.detailScreen(
        title: 'Health Details - ${widget.cattle.name ?? widget.cattle.tagId}',
        body: Consumer<HealthDetailsController>(
          builder: (context, controller, child) {
            // Initialize tab manager here where Provider context is available
            _tabManager ??= UniversalTabManager.twoTabs(
              controller: _tabController,
              tabViews: [
                HealthDetailsAnalyticsTab(
                  cattle: widget.cattle,
                  relatedRecords: controller.healthRecords,
                ),
                HealthDetailsRecordsTab(
                  cattle: widget.cattle,
                  relatedRecords: controller.healthRecords,
                ),
              ],
              labels: const ['Analytics', 'Records'],
              icons: const [Icons.analytics, Icons.list_alt],
              showFABs: const [false, true], // FAB for records tab
            );

            return _tabManager!.build(context);
          },
        ),
        onRefresh: () async {
          final controller = Provider.of<HealthDetailsController>(context, listen: false);
          await controller.refresh();
        },
      ),
    );
  }
}


