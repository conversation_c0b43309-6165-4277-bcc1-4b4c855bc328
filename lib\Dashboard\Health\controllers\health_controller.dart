import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'package:async/async.dart';
import '../models/health_record_isar.dart';
import '../models/medication_isar.dart';
import '../models/treatment_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/health_repository.dart';
import '../services/health_analytics_service.dart';
import '../../Cattle/services/cattle_repository.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // Import for ControllerState enum

/// Filter state object for decoupled filter management
/// Following the established FilterController pattern for consistency
class HealthFilterState {
  final String? searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? recordType;
  final String? status;

  const HealthFilterState({
    this.searchQuery,
    this.startDate,
    this.endDate,
    this.recordType,
    this.status,
  });

  /// Check if any filters are active
  bool get hasActiveFilters =>
      (searchQuery?.isNotEmpty ?? false) ||
      startDate != null ||
      endDate != null ||
      (recordType?.isNotEmpty ?? false) ||
      (status?.isNotEmpty ?? false);

  /// Create a copy with updated values
  HealthFilterState copyWith({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? recordType,
    String? status,
  }) {
    return HealthFilterState(
      searchQuery: searchQuery ?? this.searchQuery,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      recordType: recordType ?? this.recordType,
      status: status ?? this.status,
    );
  }

  /// Clear all filters
  static const HealthFilterState empty = HealthFilterState();
}

/// Reactive controller for the main health screen using Dual-Stream Pattern
/// Following the cattle module template: separate unfiltered and filtered streams
/// Unfiltered stream feeds analytics, filtered stream feeds UI
class HealthController extends ChangeNotifier {
  // Repositories
  final HealthRepository _healthRepository = GetIt.instance<HealthRepository>();
  final CattleRepository _cattleRepository = GetIt.instance<CattleRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Separated for filtered/unfiltered data
  StreamSubscription? _unfilteredStreamSubscription;
  StreamSubscription? _filteredStreamSubscription;

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<HealthRecordIsar> _unfilteredHealthRecords = []; // Complete dataset for analytics calculations
  List<MedicationIsar> _unfilteredMedications = []; // Complete medications dataset
  List<TreatmentIsar> _unfilteredTreatments = []; // Complete treatments dataset
  List<VaccinationIsar> _unfilteredVaccinations = []; // Complete vaccinations dataset
  List<CattleIsar> _unfilteredCattle = []; // Complete cattle dataset for analytics

  List<HealthRecordIsar> _filteredHealthRecords = []; // Filtered dataset for UI display
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  HealthAnalyticsResult _analyticsResult = HealthAnalyticsResult.empty;

  // Filter state management - decoupled from UI
  HealthFilterState _currentFilters = HealthFilterState.empty;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;

  /// Returns the filtered health records for UI display
  /// This is what the HealthRecordsTab should show
  List<HealthRecordIsar> get healthRecords => List.unmodifiable(_filteredHealthRecords);

  /// Returns the complete unfiltered health records for analytics
  /// This ensures analytics are always calculated on the full dataset
  List<HealthRecordIsar> get unfilteredHealthRecords => List.unmodifiable(_unfilteredHealthRecords);

  /// Returns the complete unfiltered cattle list for analytics
  List<CattleIsar> get unfilteredCattle => List.unmodifiable(_unfilteredCattle);

  // Main analytics object - single source of truth
  HealthAnalyticsResult get analytics => _analyticsResult;

  // Convenience getters for backward compatibility with UI
  int get totalHealthRecords => _analyticsResult.totalHealthRecords;
  int get activeRecords => _analyticsResult.activeRecords;
  int get resolvedRecords => _analyticsResult.resolvedRecords;
  int get treatmentRecords => _analyticsResult.treatmentRecords;
  int get vaccinationRecords => _analyticsResult.vaccinationRecords;
  int get medicationRecords => _analyticsResult.medicationRecords;
  double get averageHealthScore => _analyticsResult.averageHealthScore;
  double get totalHealthCosts => _analyticsResult.totalHealthCosts;
  Map<String, int> get recordsByType => _analyticsResult.recordTypeDistribution;
  Map<String, int> get recordsByStatus => _analyticsResult.statusDistribution;
  Map<String, int> get conditionDistribution => _analyticsResult.conditionDistribution;
  int get cattleWithRecentTreatments => _analyticsResult.cattleWithRecentTreatments;
  int get overdueVaccinations => _analyticsResult.overdueVaccinations;
  List<String> get chronicConditions => _analyticsResult.chronicConditions;
  double get treatmentSuccessRate => _analyticsResult.treatmentSuccessRate;

  // Additional getters for analytics tabs
  int get completedRecords => _analyticsResult.resolvedRecords;
  List<CattleIsar> get cattle => _unfilteredCattle;

  // Filter state access
  HealthFilterState get currentFilters => _currentFilters;

  // Constructor
  HealthController() {
    _initializeStreamListeners();
  }

  /// Initialize stream listeners for real-time updates using Dual-Stream Pattern
  /// Critical: Separate streams for unfiltered (analytics) and filtered (UI) data
  void _initializeStreamListeners() {
    // Primary stream: Unfiltered data for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    final combinedStream = StreamZip([
      _healthRepository.watchAllHealthRecords(),
      _healthRepository.watchAllMedications(),
      _healthRepository.watchAllTreatments(),
      _healthRepository.watchAllVaccinations(),
      _cattleRepository.watchAllCattle(),
    ]);

    _unfilteredStreamSubscription = combinedStream.listen((data) {
      _handleUnfilteredDataUpdate(
        data[0] as List<HealthRecordIsar>,
        data[1] as List<MedicationIsar>,
        data[2] as List<TreatmentIsar>,
        data[3] as List<VaccinationIsar>,
        data[4] as List<CattleIsar>,
      );
    });

    // Initially, filtered data equals unfiltered data (no filters applied)
    _filteredHealthRecords = _unfilteredHealthRecords;
    _hasActiveFilters = false;
  }

  /// Handle unfiltered data updates - Used for analytics calculations
  /// Critical: This method ONLY updates analytics and unfiltered data
  void _handleUnfilteredDataUpdate(
    List<HealthRecordIsar> unfilteredHealthRecords,
    List<MedicationIsar> unfilteredMedications,
    List<TreatmentIsar> unfilteredTreatments,
    List<VaccinationIsar> unfilteredVaccinations,
    List<CattleIsar> unfilteredCattle,
  ) async {
    try {
      // Update the complete unfiltered datasets
      _unfilteredHealthRecords = unfilteredHealthRecords;
      _unfilteredMedications = unfilteredMedications;
      _unfilteredTreatments = unfilteredTreatments;
      _unfilteredVaccinations = unfilteredVaccinations;
      _unfilteredCattle = unfilteredCattle;

      // ALWAYS calculate analytics on the complete unfiltered dataset
      // This ensures analytics remain accurate regardless of applied filters
      if (_unfilteredHealthRecords.isEmpty && _unfilteredMedications.isEmpty &&
          _unfilteredTreatments.isEmpty && _unfilteredVaccinations.isEmpty) {
        _analyticsResult = HealthAnalyticsResult.empty;
      } else {
        _calculateAnalytics();
      }

      // If no filters are active, update filtered data to match unfiltered
      if (!_hasActiveFilters) {
        _filteredHealthRecords = List.from(_unfilteredHealthRecords);
      }

      _setState(ControllerState.loaded);
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling unfiltered data update: $e');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to update unfiltered data: $e';
      notifyListeners();
    }
  }

  /// Calculate analytics using the dedicated service
  /// Critical: ALWAYS uses unfiltered data to ensure accurate analytics
  void _calculateAnalytics() {
    _analyticsResult = HealthAnalyticsService.calculate(
      _unfilteredHealthRecords, // Use unfiltered data for accurate analytics
      _unfilteredMedications,
      _unfilteredTreatments,
      _unfilteredVaccinations,
      _unfilteredCattle,
    );
  }

  /// Apply filters using the FilterController pattern for decoupled filter management
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  void applyFilters(HealthFilterState filterState) async {
    // Update current filter state
    _currentFilters = filterState;

    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredStreamSubscription?.cancel();

    // Check if filters are active
    _hasActiveFilters = filterState.hasActiveFilters;

    if (_hasActiveFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery(filterState);

      // Create separate stream for filtered data
      _filteredStreamSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _handleFilteredDataUpdate(filteredList);
      });
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredHealthRecords = List.from(_unfilteredHealthRecords);
      notifyListeners();
    }
  }

  /// Convenience method for backward compatibility and simple filter updates
  void updateFilters({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? recordType,
    String? status,
  }) {
    final newFilters = _currentFilters.copyWith(
      searchQuery: searchQuery,
      startDate: startDate,
      endDate: endDate,
      recordType: recordType,
      status: status,
    );
    applyFilters(newFilters);
  }

  /// Clear all filters
  void clearFilters() {
    applyFilters(HealthFilterState.empty);
  }

  /// Handle filtered data updates - Used for UI display only
  /// Critical: This method NEVER affects analytics calculations
  void _handleFilteredDataUpdate(List<HealthRecordIsar> filteredList) {
    try {
      // Update only the filtered dataset for UI display
      _filteredHealthRecords = filteredList;

      // Do NOT recalculate analytics here - they're handled by unfiltered stream
      // This ensures analytics always reflect the complete dataset

      notifyListeners();
    } catch (e) {
      debugPrint('Error handling filtered data update: $e');
      // Don't change state to error for filtered data issues
      notifyListeners();
    }
  }

  /// Build filtered Isar query based on FilterState object
  /// This method dynamically constructs database queries for optimal performance
  dynamic _buildFilteredQuery(HealthFilterState filterState) {
    // Start with base query and build the chain using dynamic typing
    dynamic currentQuery = _isar.healthRecordIsars.where();

    // Apply search filter at database level
    if (filterState.searchQuery?.isNotEmpty ?? false) {
      final searchTerm = filterState.searchQuery!.toLowerCase();
      currentQuery = currentQuery.filter().group((q) => q
          .conditionContains(searchTerm, caseSensitive: false)
          .or()
          .notesContains(searchTerm, caseSensitive: false));
    }

    // Apply date range filters at database level
    if (filterState.startDate != null) {
      currentQuery = currentQuery.filter().dateGreaterThan(filterState.startDate!);
    }
    if (filterState.endDate != null) {
      // Add one day to make end date inclusive
      final inclusiveEndDate = filterState.endDate!.add(const Duration(days: 1));
      currentQuery = currentQuery.filter().dateLessThan(inclusiveEndDate);
    }

    // Apply record type filter
    if (filterState.recordType?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().recordTypeEqualTo(filterState.recordType);
    }

    // Apply status filter
    if (filterState.status?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().statusEqualTo(filterState.status);
    }

    // Apply sorting at database level for optimal performance
    currentQuery = currentQuery.sortByDateDesc(); // Default sort by date (newest first)

    return currentQuery;
  }

  // CRUD Methods - Single Source of Truth pattern
  // These methods only update the database, stream handles UI updates
  // Exceptions bubble up naturally to be handled by higher-level error handlers

  /// Add new health record - only updates database, stream handles UI update
  Future<void> addHealthRecord(HealthRecordIsar record) async {
    await _healthRepository.saveHealthRecord(record);
    // Stream will handle the UI update automatically
  }

  /// Update health record - only updates database, stream handles UI update
  Future<void> updateHealthRecord(HealthRecordIsar updatedRecord) async {
    await _healthRepository.saveHealthRecord(updatedRecord);
    // Stream will handle the UI update automatically
  }

  /// Delete health record - only updates database, stream handles UI update
  Future<void> deleteHealthRecord(int recordId) async {
    await _healthRepository.deleteHealthRecord(recordId);
    // Stream will handle the UI update automatically
  }

  /// Refresh all data - compatible with analytics tab refresh functionality
  Future<void> refresh() async {
    try {
      // Force analytics recalculation on unfiltered data
      if (_unfilteredHealthRecords.isNotEmpty || _unfilteredMedications.isNotEmpty ||
          _unfilteredTreatments.isNotEmpty || _unfilteredVaccinations.isNotEmpty) {
        _calculateAnalytics();
      }

      // Notify listeners of the refresh
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing health data: $e\n$stackTrace');
      throw Exception('Failed to refresh health data: ${e.toString()}');
    }
  }

  // Helper methods
  CattleIsar? getCattle(String? cattleBusinessId) {
    if (cattleBusinessId == null) return null;
    try {
      return _unfilteredCattle.firstWhere(
        (cattle) => cattle.businessId == cattleBusinessId,
      );
    } catch (e) {
      return null;
    }
  }

  String getCattleName(String? cattleBusinessId) {
    final cattle = getCattle(cattleBusinessId);
    return cattle?.name ?? 'Unknown Cattle';
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  // _setError method removed - unused

  @override
  void dispose() {
    _unfilteredStreamSubscription?.cancel();
    _filteredStreamSubscription?.cancel();
    super.dispose();
  }
}
