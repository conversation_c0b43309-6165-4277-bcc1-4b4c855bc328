import 'package:flutter/material.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/health_record_isar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';
import '../../../constants/app_colors.dart';

// --- Constants ---
class _AppStrings {
  static const String addHealthTitle = 'Add Health Record';
  static const String editHealthTitle = 'Edit Health Record';
  static const String cattleLabel = 'Cattle';
  static const String dateLabel = 'Date';
  static const String conditionLabel = 'Condition/Diagnosis';
  static const String treatmentLabel = 'Treatment';
  static const String veterinarianLabel = 'Veterinarian';
  static const String costLabel = 'Cost';
  static const String notesLabel = 'Notes';

  // Validation messages

  // Success messages
  static const String addSuccess = 'Health record added successfully';
  static const String updateSuccess = 'Health record updated successfully';
  static const String saveError = 'Error saving health record';

  // Validation messages
  static const String cattleRequired = 'Please select a cattle';
  static const String conditionRequired = 'Please enter a condition or diagnosis';
  static const String treatmentRequired = 'Please enter a treatment';
  static const String dateRequired = 'Please select a date';
}

class HealthRecordFormDialog extends StatefulWidget {
  final HealthRecordIsar? healthRecord;
  final List<CattleIsar> cattle;
  final Future<void> Function(HealthRecordIsar)? onSave;

  const HealthRecordFormDialog({
    Key? key,
    this.healthRecord,
    required this.cattle,
    this.onSave,
  }) : super(key: key);

  @override
  State<HealthRecordFormDialog> createState() => _HealthRecordFormDialogState();
}

class _HealthRecordFormDialogState extends State<HealthRecordFormDialog> {
  final _formKey = GlobalKey<FormState>();

  // --- State Controllers and Variables ---
  // Use controllers for text fields and simple variables for other types.
  late final TextEditingController _conditionController;
  late final TextEditingController _treatmentController;
  late final TextEditingController _veterinarianController;
  late final TextEditingController _costController;
  late final TextEditingController _notesController;

  String? _selectedCattleId;
  DateTime _selectedDate = DateTime.now();
  bool _showOptionalFields = false; // Toggle for optional fields visibility


  @override
  void initState() {
    super.initState();
    final record = widget.healthRecord;

    // Initialize controllers with existing data or empty strings
    _conditionController = TextEditingController(text: record?.details ?? record?.condition ?? '');
    _treatmentController = TextEditingController(text: record?.treatment ?? '');
    _veterinarianController = TextEditingController(text: record?.veterinarian ?? '');
    _costController = TextEditingController(text: record?.cost?.toString() ?? '');
    _notesController = TextEditingController(text: record?.notes ?? '');

    // Set initial values for non-text fields
    _selectedDate = record?.date ?? DateTime.now();

    // Safely set the initial cattle ID
    if (record != null && widget.cattle.any((c) => c.tagId == record.cattleTagId)) {
      _selectedCattleId = record.cattleTagId;
    } else if (widget.cattle.isNotEmpty) {
      _selectedCattleId = widget.cattle.first.tagId;
    }

    // Show optional fields if any optional data exists
    if (record != null &&
        (record.veterinarian?.isNotEmpty == true ||
         record.cost != null && record.cost! > 0 ||
         record.notes?.isNotEmpty == true)) {
      _showOptionalFields = true;
    }
  }

  @override
  void dispose() {
    // Dispose all controllers
    _conditionController.dispose();
    _treatmentController.dispose();
    _veterinarianController.dispose();
    _costController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _handleSave() async {
    // Validate the form. If it fails, do nothing.
    if (!(_formKey.currentState?.validate() ?? false)) {
      return;
    }



    try {
      // Create the record object from form data
      final record = HealthRecordIsar.create(
        businessId: widget.healthRecord?.businessId,
        cattleTagId: _selectedCattleId ?? '',
        date: _selectedDate,
        details: _conditionController.text.trim(),
        treatment: _treatmentController.text.trim(),
        notes: _notesController.text.trim(),
        cost: double.tryParse(_costController.text.trim()),
        veterinarian: _veterinarianController.text.trim(),
      );

      // Await the save operation if a callback is provided
      if (widget.onSave != null) {
        await widget.onSave!(record);
      }
      
      // Show success message and pop the dialog if the widget is still mounted
      if (mounted) {
        MessageUtils.showSuccess(
          context,
          widget.healthRecord != null
              ? _AppStrings.updateSuccess
              : _AppStrings.addSuccess,
        );
        Navigator.of(context).pop(record); // Pop with the saved record
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, '${_AppStrings.saveError}: $e');
      }
    } finally {
      // Ensure the saving state is always reset
      if (mounted) {

      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show a loading dialog if cattle list is empty and being fetched
    if (widget.cattle.isEmpty) {
      return const Dialog(
        child: SizedBox(
          height: 200,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text("Loading Cattle Data..."),
              ],
            ),
          ),
        ),
      );
    }
    
    return UniversalFormDialog(
      title: widget.healthRecord == null ? _AppStrings.addHealthTitle : _AppStrings.editHealthTitle,
      headerIcon: Icons.medical_services,
      formContent: _buildFormContent(),
      actionButtons: UniversalDialogButtons.cancelSaveRow(
        onCancel: () => Navigator.of(context).pop(),
        onSave: _handleSave,
      ),
    );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Cattle Dropdown
          UniversalFormField.dropdownField<String>(
            label: _AppStrings.cattleLabel,
            value: _selectedCattleId,
            items: widget.cattle.map((cattle) {
              final cattleName = cattle.name ?? 'Unknown';
              final tagId = cattle.tagId ?? '';
              final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
              return DropdownMenuItem(
                value: cattle.tagId,
                child: Text(displayName, overflow: TextOverflow.ellipsis),
              );
            }).toList(),
            onChanged: (value) => setState(() => _selectedCattleId = value),
            prefixIcon: Icons.pets,
            prefixIconColor: Colors.green, // Changed from forbidden brown
            validator: (value) => UniversalFormField.dropdownValidator(value, 'cattle'),
          ),
          UniversalFormField.spacing,

          // Date Field
          UniversalFormField.dateField(
            context: context,
            label: _AppStrings.dateLabel,
            value: _selectedDate,
            onChanged: (date) {
              setState(() {
                _selectedDate = date ?? DateTime.now();
              });
            },
            prefixIcon: Icons.calendar_today,
            prefixIconColor: Colors.blue,
            lastDate: DateTime.now(),
          ),
          UniversalFormField.spacing,

          // Condition Field
          UniversalFormField.textField(
            label: _AppStrings.conditionLabel,
            controller: _conditionController,
            prefixIcon: Icons.medical_information,
            prefixIconColor: Colors.red,
            validator: (value) => UniversalFormField.requiredValidator(value, 'condition'),
          ),
          UniversalFormField.spacing,

          // Treatment Field
          UniversalFormField.textField(
            label: _AppStrings.treatmentLabel,
            controller: _treatmentController,
            prefixIcon: Icons.healing,
            prefixIconColor: Colors.green,
            validator: (value) => UniversalFormField.requiredValidator(value, 'treatment'),
          ),
          UniversalFormField.spacing,

          // Optional Fields Toggle
          UniversalFormField.toggleSwitch(
            label: 'Optional Fields',
            value: _showOptionalFields,
            onChanged: (value) {
              setState(() {
                _showOptionalFields = value;
              });
            },
            activeColor: AppColors.healthHeader,
          ),

          // Optional Fields (shown when toggle is enabled)
          if (_showOptionalFields) ...[
            UniversalFormField.spacing,

            // Veterinarian Field
            UniversalFormField.textField(
              label: _AppStrings.veterinarianLabel,
              controller: _veterinarianController,
              prefixIcon: Icons.person,
              prefixIconColor: Colors.purple,
            ),
            UniversalFormField.spacing,

            // Cost Field
            UniversalFormField.numberField(
              label: _AppStrings.costLabel,
              controller: _costController,
              allowDecimals: true,
              prefixIcon: Icons.attach_money,
              prefixIconColor: Colors.indigo,
            ),
            UniversalFormField.spacing,

            // Notes Field
            UniversalFormField.multilineField(
              label: _AppStrings.notesLabel,
              controller: _notesController,
              maxLines: 3,
              prefixIcon: Icons.note_alt,
              prefixIconColor: Colors.teal,
            ),
          ],
        ],
      ),
    );
  }



}