import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/health_controller.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';

class VaccinationsTab extends StatefulWidget {
  final HealthController? controller; // Made optional to support Provider pattern

  const VaccinationsTab({
    Key? key,
    this.controller, // Optional - will use Provider if not provided
  }) : super(key: key);

  @override
  State<VaccinationsTab> createState() => _VaccinationsTabState();
}

class _VaccinationsTabState extends State<VaccinationsTab> {
  /// Get controller from either widget prop or Provider
  HealthController get _controller => widget.controller ?? context.read<HealthController>();

  @override
  Widget build(BuildContext context) {
    return Consumer<HealthController>(
      builder: (context, controller, child) {
        // For now, show a placeholder until vaccinations are fully implemented
        return UniversalEmptyState.health(
          title: 'Vaccinations Coming Soon',
          message: 'Vaccination records functionality will be available in the next update',
          icon: Icons.vaccines,
          color: AppColors.healthHeader,
        );
      },
    );
  }
}
