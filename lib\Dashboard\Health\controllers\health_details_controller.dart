import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';

import '../../Cattle/models/cattle_isar.dart';
import '../models/health_record_isar.dart';
import '../services/health_repository.dart';


/// Controller for managing health details screen state and data
class HealthDetailsController extends ChangeNotifier {
  final HealthRepository _healthRepository = GetIt.instance<HealthRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  bool _isLoading = false;
  String? _error;
  CattleIsar? _cattle;

  // Health data
  List<HealthRecordIsar> _healthRecords = [];

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  CattleIsar? get cattle => _cattle;
  List<HealthRecordIsar> get healthRecords => _healthRecords;

  // Computed properties for analytics
  int get totalHealthRecords => _healthRecords.length;
  int get vaccinationRecords => _healthRecords.where((r) => r.recordType?.toLowerCase() == 'vaccination').length;
  int get treatmentRecords => _healthRecords.where((r) => r.recordType?.toLowerCase() == 'treatment').length;
  int get checkupRecords => _healthRecords.where((r) => r.recordType?.toLowerCase() == 'checkup').length;
  
  List<HealthRecordIsar> get recentRecords {
    final sorted = List<HealthRecordIsar>.from(_healthRecords)
      ..sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
    return sorted.take(5).toList();
  }

  bool get hasActiveIssues {
    return _healthRecords.any((r) => r.status?.toLowerCase() == 'active');
  }

  /// Initialize controller with cattle data
  void initialize(CattleIsar cattle) {
    _cattle = cattle;
    _loadHealthData();
  }

  /// Load all health-related data for the cattle
  Future<void> _loadHealthData() async {
    if (_cattle == null) return;

    try {
      _setLoading(true);
      _clearError();

      // Load all health records and filter for this cattle
      final allHealthRecords = await _isar.healthRecordIsars.where().findAll();
      _healthRecords = allHealthRecords.where((record) => record.cattleId == _cattle!.businessId).toList();

      _setLoading(false);
    } catch (e) {
      _setError('Failed to load health data: $e');
      _setLoading(false);
    }
  }

  /// Refresh all data
  Future<void> refresh() async {
    await _loadHealthData();
  }

  /// Add a new health record
  Future<bool> addHealthRecord(HealthRecordIsar record) async {
    try {
      _setLoading(true);
      await _healthRepository.saveHealthRecord(record);
      await _loadHealthData(); // Refresh data
      return true;
    } catch (e) {
      _setError('Failed to add health record: $e');
      _setLoading(false);
      return false;
    }
  }

  /// Update an existing health record
  Future<bool> updateHealthRecord(HealthRecordIsar record) async {
    try {
      _setLoading(true);
      await _healthRepository.saveHealthRecord(record);
      await _loadHealthData(); // Refresh data
      return true;
    } catch (e) {
      _setError('Failed to update health record: $e');
      _setLoading(false);
      return false;
    }
  }

  /// Delete a health record
  Future<bool> deleteHealthRecord(int recordId) async {
    try {
      _setLoading(true);
      await _healthRepository.deleteHealthRecord(recordId);
      await _loadHealthData(); // Refresh data
      return true;
    } catch (e) {
      _setError('Failed to delete health record: $e');
      _setLoading(false);
      return false;
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    // Clean up any resources
    super.dispose();
  }
}
