import 'package:flutter/material.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/treatment_isar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_dialog_buttons.dart';
import '../../../constants/app_layout.dart';

// --- Constants ---
class _AppStrings {
  static const String addTreatmentTitle = 'Add Treatment';
  static const String editTreatmentTitle = 'Edit Treatment';
  static const String cattleLabel = 'Select Cattle';
  static const String conditionLabel = 'Condition';
  static const String treatmentLabel = 'Treatment';
  static const String dosageLabel = 'Dosage';
  static const String dateLabel = 'Date';
  static const String veterinarianLabel = 'Veterinarian';
  static const String costLabel = 'Cost';
  static const String notesLabel = 'Notes';

  static const String cattleRequired = 'Please select a cattle';
  static const String conditionRequired = 'Please enter a condition';
  static const String treatmentRequired = 'Please enter a treatment';
  static const String dateRequired = 'Please select a date';
  static const String loadingError = 'Could not load cattle information. Please try again.';
}

class TreatmentFormDialog extends StatefulWidget {
  final TreatmentIsar? treatment;
  final List<CattleIsar> cattle;
  final String? cattleId;
  final Future<void> Function(TreatmentIsar)? onSave;

  const TreatmentFormDialog({
    Key? key,
    this.treatment,
    required this.cattle,
    this.cattleId,
    this.onSave,
  }) : super(key: key);

  @override
  State<TreatmentFormDialog> createState() => _TreatmentFormDialogState();
}

class _TreatmentFormDialogState extends State<TreatmentFormDialog> {
  final _formKey = GlobalKey<FormState>();

  // --- Controllers ---
  late TextEditingController _conditionController;
  late TextEditingController _treatmentController;
  late TextEditingController _veterinarianController;
  late TextEditingController _dosageController;
  late TextEditingController _costController;
  late TextEditingController _notesController;

  // --- State Variables ---
  String? _selectedCattleId;
  DateTime? _selectedDate;
  bool _showOptionalFields = false; // Toggle for optional fields visibility
  int? _isarId;

  List<CattleIsar> _cattleList = [];
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();

    // Initialize controllers
    _conditionController = TextEditingController();
    _treatmentController = TextEditingController();
    _veterinarianController = TextEditingController();
    _dosageController = TextEditingController();
    _costController = TextEditingController();
    _notesController = TextEditingController();

    _loadCattleData();
    
    if (widget.treatment != null) {
      // --- Editing existing treatment ---
      final treatment = widget.treatment!;
      _isarId = treatment.id;
      _selectedCattleId = treatment.cattleId;
      _conditionController.text = treatment.condition ?? '';
      _treatmentController.text = treatment.treatment ?? '';
      _veterinarianController.text = treatment.veterinarian ?? '';
      _dosageController.text = treatment.dosage ?? '';
      _selectedDate = treatment.date ?? DateTime.now();
      _costController.text = treatment.cost ?? '0.0';
      _notesController.text = treatment.notes ?? '';
    } else {
      // --- Creating new treatment ---
      _selectedCattleId = widget.cattleId;
      _selectedDate = DateTime.now();
      _costController.text = '0.0';
    }
  }

  @override
  void dispose() {
    _conditionController.dispose();
    _treatmentController.dispose();
    _veterinarianController.dispose();
    _dosageController.dispose();
    _costController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadCattleData() async {
    try {
      if (mounted) {
        setState(() {
          _cattleList = widget.cattle;
          if (_cattleList.isNotEmpty && (_selectedCattleId == null || _selectedCattleId!.isEmpty)) {
            // Initialize with the first cattle's business ID (not Isar ID)
            _selectedCattleId = _cattleList.first.businessId ?? '';
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        MessageUtils.showError(context, _AppStrings.loadingError);
      }
    }
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Store navigator before async operations
    final navigator = Navigator.of(context);

    setState(() {
      _isSaving = true;
    });

    try {
      // Validate required fields
      if (_selectedCattleId == null || _selectedCattleId!.isEmpty) {
        throw Exception(_AppStrings.cattleRequired);
      }
      if (_conditionController.text.trim().isEmpty) {
        throw Exception(_AppStrings.conditionRequired);
      }
      if (_treatmentController.text.trim().isEmpty) {
        throw Exception(_AppStrings.treatmentRequired);
      }
      if (_selectedDate == null) {
        throw Exception(_AppStrings.dateRequired);
      }

      final treatmentIsar = TreatmentIsar(
        cattleId: _selectedCattleId!,
        date: _selectedDate!,
        condition: _conditionController.text.trim(),
        treatment: _treatmentController.text.trim(),
        veterinarian: _veterinarianController.text.trim(),
        dosage: _dosageController.text.trim(),
        cost: _costController.text.trim(),
        notes: _notesController.text.trim(),
        status: 'Active',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Preserve Isar ID for edits
      if (_isarId != null) {
        treatmentIsar.id = _isarId!;
      }

      if (mounted) {
        if (widget.onSave != null) {
          // Use the onSave callback if provided
          await widget.onSave!(treatmentIsar);
          if (mounted) {
            setState(() {
              _isSaving = false;
            });
            navigator.pop();
          }
        } else {
          // Return the treatment object directly
          setState(() {
            _isSaving = false;
          });
          navigator.pop(treatmentIsar);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
        MessageUtils.showError(context,
            'Error: ${e.toString().replaceAll('Exception: ', '')}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Dialog(
        child: SizedBox(
          height: 200,
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    return widget.treatment == null
        ? UniversalFormDialog(
            title: _AppStrings.addTreatmentTitle,
            headerIcon: Icons.medical_services, // Treatment-specific icon for add
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _handleSave,
              isAdding: _isSaving,
            ),
          )
        : UniversalFormDialog(
            title: _AppStrings.editTreatmentTitle,
            headerIcon: Icons.medical_services, // Treatment-specific icon for edit
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _handleSave,
              isUpdating: _isSaving,
            ),
          );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Cattle Selection Dropdown
          UniversalFormField.dropdownField<String>(
            label: _AppStrings.cattleLabel,
            value: _cattleList.any((c) => c.businessId == _selectedCattleId)
                ? _selectedCattleId
                : null,
            items: _cattleList.map((cattle) {
              return DropdownMenuItem<String>(
                value: cattle.businessId,
                child: Text(
                  '${cattle.name} (Tag ID: ${cattle.tagId})',
                  overflow: TextOverflow.ellipsis,
                ),
              );
            }).toList(),
            onChanged: (value) {
              if (!_isSaving) {
                setState(() {
                  _selectedCattleId = value;
                });
              }
            },
            enabled: !_isSaving,
            prefixIcon: Icons.pets,
            prefixIconColor: Colors.green, // Changed from brown (forbidden)
            validator: (value) => UniversalFormField.dropdownValidator(value, 'cattle'),
          ),
          UniversalFormField.spacing,

          // Condition Field
          UniversalFormField.textField(
            label: _AppStrings.conditionLabel,
            controller: _conditionController,
            prefixIcon: Icons.medical_information,
            prefixIconColor: Colors.red,
            validator: (value) => UniversalFormField.requiredValidator(value, 'condition'),
            enabled: !_isSaving,
          ),
          UniversalFormField.spacing,

          // Treatment Field
          UniversalFormField.textField(
            label: _AppStrings.treatmentLabel,
            controller: _treatmentController,
            prefixIcon: Icons.healing,
            prefixIconColor: Colors.teal, // Changed from green to avoid repetition
            validator: (value) => UniversalFormField.requiredValidator(value, 'treatment'),
            enabled: !_isSaving,
          ),
          UniversalFormField.spacing,

          // Date Field
          UniversalFormField.dateField(
            context: context,
            label: _AppStrings.dateLabel,
            value: _selectedDate,
            onChanged: (date) {
              setState(() {
                _selectedDate = date;
              });
            },
            prefixIcon: Icons.calendar_today,
            prefixIconColor: Colors.blue,
            validator: (date) {
              if (date == null) {
                return _AppStrings.dateRequired;
              }
              return null;
            },
            lastDate: DateTime.now().add(const Duration(days: 365)),
            enabled: !_isSaving,
          ),
          UniversalFormField.sectionSpacing,

          // Optional Information Toggle Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _showOptionalFields = !_showOptionalFields;
                });
              },
              icon: Icon(
                _showOptionalFields
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: const Color(0xFF2E7D32),
              ),
              label: Text(
                _showOptionalFields
                    ? 'Hide Optional Information'
                    : 'Show Optional Information',
                style: const TextStyle(
                  color: Color(0xFF2E7D32),
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(
                  color: Color(0xFF2E7D32),
                  width: 1.5,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          // Optional Fields - Conditionally Displayed
          if (_showOptionalFields) ...[
            UniversalFormField.spacing,

            // Dosage Field
            UniversalFormField.textField(
              label: _AppStrings.dosageLabel,
              controller: _dosageController,
              prefixIcon: Icons.medication,
              prefixIconColor: Colors.purple,
              enabled: !_isSaving,
            ),
            UniversalFormField.spacing,

            // Veterinarian Field
            UniversalFormField.textField(
              label: _AppStrings.veterinarianLabel,
              controller: _veterinarianController,
              prefixIcon: Icons.person,
              prefixIconColor: Colors.indigo,
              enabled: !_isSaving,
            ),
            UniversalFormField.spacing,

            // Cost Field
            UniversalFormField.numberField(
              label: _AppStrings.costLabel,
              controller: _costController,
              allowDecimals: true,
              prefixIcon: Icons.attach_money,
              prefixIconColor: Colors.lightGreen,
              validator: (value) {
                if (value != null && value.trim().isNotEmpty) {
                  return UniversalFormField.numberValidator(value);
                }
                return null;
              },
              enabled: !_isSaving,
            ),
            UniversalFormField.spacing,

            // Notes Field
            UniversalFormField.multilineField(
              label: _AppStrings.notesLabel,
              controller: _notesController,
              maxLines: 3,
              prefixIcon: Icons.notes,
              prefixIconColor: Colors.cyan,
              enabled: !_isSaving,
            ),
          ],
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
