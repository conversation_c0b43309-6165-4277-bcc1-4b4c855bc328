import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../dialogs/milk_sale_entry_dialog.dart';
import '../models/milk_sale_isar.dart';
import '../controllers/milk_controller.dart';
import '../../Farm Setup/services/farm_setup_repository.dart';
import '../../widgets/universal_record_card.dart';
import '../../widgets/filters/filters.dart';
import '../../widgets/filters/filter_layout.dart';

import '../../../utils/message_utils.dart';
import 'package:get_it/get_it.dart';

class MilkSalesTab extends StatefulWidget {
  final MilkController? controller;

  const MilkSalesTab({
    Key? key,
    this.controller,
  }) : super(key: key);

  @override
  State<MilkSalesTab> createState() => _MilkSalesTabState();
}

class _MilkSalesTabState extends State<MilkSalesTab> {
  late FilterController _filterController;

  // Currency settings
  String _currencySymbol = r'$';
  bool _symbolBeforeAmount = true;

  /// Get controller from either widget prop or Provider
  MilkController get _controller => widget.controller ?? context.read<MilkController>();

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    _loadCurrencySettings();
  }

  @override
  void dispose() {
    _filterController.dispose();
    super.dispose();
  }

  /// Load currency settings from farm setup
  Future<void> _loadCurrencySettings() async {
    try {
      final farmSetupRepository = GetIt.instance<FarmSetupRepository>();
      final currencySettings = await farmSetupRepository.getCurrencySettings();

      if (mounted && currencySettings != null) {
        setState(() {
          // Remove escape characters from currency symbol
          _currencySymbol = currencySettings.currencySymbol.replaceAll(r'\', '');
          _symbolBeforeAmount = currencySettings.symbolBeforeAmount;
        });
      }
    } catch (e) {
      // Use default values if loading fails
      if (mounted) {
        setState(() {
          _currencySymbol = r'$';
          _symbolBeforeAmount = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return _buildContent();
      },
    );
  }

  Widget _buildContent() {
    // Check if we have data to display
    if (_controller.totalMilkSales == 0) {
      return _buildEmptyState(true);
    }

    // Get milk sales data
    final sales = _controller.unfilteredMilkSales;
    final allSalesCount = _controller.totalMilkSales;

    return Column(
      children: [
        // Universal Filter Layout
        UniversalFilterLayout(
          controller: _filterController,
          theme: FilterTheme.milk,
          moduleName: 'milk_sales',
          sortFields: const [...SortField.commonFields, ...SortField.milkFields],
          searchHint: 'Search milk sales by date, amount, or notes...',
          totalCount: allSalesCount,
          filteredCount: sales.length,
        ),

        // Sales List
        Expanded(
          child: sales.isEmpty
              ? _buildEmptyState(allSalesCount == 0)
              : RefreshIndicator(
                  onRefresh: () async {
                    await _controller.refresh();
                    _filterController.clearAllApplied();
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(kPaddingMedium),
                    itemCount: sales.length,
                    itemBuilder: (context, index) {
                      final sale = sales[index];
                      return _buildSaleCard(sale);
                    },
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(bool isCompletelyEmpty) {
    const tabColor = AppColors.milkHeader;

    if (isCompletelyEmpty) {
      return UniversalTabEmptyState.forTab(
        title: 'No Milk Sales',
        message: 'Add your first milk sale to start tracking sales.',
        tabColor: tabColor,
        tabIndex: 2, // Sales tab
        action: TabEmptyStateActions.addFirstRecord(
          onPressed: () => _showAddMilkSaleDialog(),
          tabColor: tabColor,
        ),
      );
    } else {
      return UniversalTabEmptyState.forTab(
        title: 'No Matching Sales',
        message: 'Try adjusting your filters or search terms',
        tabColor: tabColor,
        tabIndex: 2, // Sales tab
        action: TabEmptyStateActions.clearFilters(
          onPressed: () => _clearAllFilters(),
          tabColor: tabColor,
        ),
      );
    }
  }

  Widget _buildSaleCard(MilkSaleIsar sale) {
    // Format date
    String dateText = sale.date != null
        ? DateFormat('MMM dd, yyyy').format(sale.date!)
        : 'Unknown Date';

    // Row 1: Date + Milk sold quantity
    String quantitySold = '${(sale.quantity ?? 0.0).toStringAsFixed(1)}L';

    // Row 2: Rate = Amount (rate per liter and total amount)
    String rateText = '${_formatCurrency(sale.ratePerLiter ?? 0.0)}/L';
    String totalAmountText = _formatCurrency(sale.totalAmount ?? 0.0);

    // Row 3: Payment status
    String paymentStatus = sale.isPaid == true ? 'Paid' : 'Pending';

    return UniversalRecordCard(
      row1Left: dateText,
      row1Right: quantitySold,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: Icons.water_drop,
      row2Left: rateText,
      row2Right: totalAmountText,
      row2LeftIcon: Icons.local_offer, // Price tag icon for rate
      row2RightIcon: Icons.account_balance_wallet, // Wallet icon for total amount
      row3Left: paymentStatus,
      row3Right: _getUsageInfo(sale),
      row3LeftIcon: sale.isPaid == true ? Icons.check_circle : Icons.pending,
      row3RightIcon: Icons.info_outline,
      notes: sale.notes?.isNotEmpty == true ? sale.notes : null,
      primaryColor: AppColors.milkHeader,
      onTap: () => _showEditMilkSaleDialog(sale),
      onEdit: () => _showEditMilkSaleDialog(sale),
      onDelete: () => _showDeleteConfirmation(sale),
    );
  }

  /// Format currency display
  String _formatCurrency(double amount) {
    final formattedAmount = amount.toStringAsFixed(2);
    return _symbolBeforeAmount
        ? '${_currencySymbol}${formattedAmount}'
        : '${formattedAmount}${_currencySymbol}';
  }

  /// Get usage information for display
  String _getUsageInfo(MilkSaleIsar sale) {
    final homeUsage = sale.homeUsage ?? 0.0;
    final calfUsage = sale.calfUsage ?? 0.0;

    if (homeUsage > 0 || calfUsage > 0) {
      return 'H:${homeUsage.toStringAsFixed(1)}L C:${calfUsage.toStringAsFixed(1)}L';
    }
    return 'No usage recorded';
  }

  /// Show add milk sale dialog
  void _showAddMilkSaleDialog() async {
    // Calculate available milk for today
    final availableMilk = await _calculateAvailableMilk();

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => MilkSaleEntryDialog(
        selectedDate: DateTime.now(),
        availableMilk: availableMilk,
        onSave: (sale) async {
          await _controller.addMilkSale(sale);
        },
      ),
    );
  }

  /// Show edit milk sale dialog
  void _showEditMilkSaleDialog(MilkSaleIsar sale) async {
    // Calculate available milk for the sale date, excluding the current sale being edited
    final availableMilk = await _calculateAvailableMilkForDate(
      sale.date ?? DateTime.now(),
      excludeSale: sale,
    );

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => MilkSaleEntryDialog(
        selectedDate: sale.date ?? DateTime.now(),
        availableMilk: availableMilk,
        existingSale: sale,
        onSave: (updatedSale) async {
          await _controller.addMilkSale(updatedSale); // Use addMilkSale for both add and update
        },
      ),
    );
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation(MilkSaleIsar sale) async {
    final confirmed = await MilkMessageUtils.showMilkSaleDeleteConfirmation(
      context,
      amount: _formatCurrency(sale.totalAmount ?? 0.0),
      recordId: sale.businessId,
    );

    if (confirmed == true && mounted) {
      try {
        await _controller.deleteMilkSale(sale.id); // Use id instead of businessId
        MilkMessageUtils.showSuccess(context, 'Milk sale deleted successfully');
      } catch (e) {
        MilkMessageUtils.showError(context, 'Failed to delete milk sale: $e');
      }
    }
  }

  /// Clear all active filters
  void _clearAllFilters() {
    _filterController.clearAllApplied();
  }

  /// Calculate available milk for today
  Future<double> _calculateAvailableMilk() async {
    return _calculateAvailableMilkForDate(DateTime.now());
  }

  /// Calculate available milk for a specific date
  /// If excludeSale is provided, that sale's usage will be excluded from the calculation
  /// (useful when editing an existing sale)
  Future<double> _calculateAvailableMilkForDate(DateTime date, {MilkSaleIsar? excludeSale}) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    // Calculate total milk produced on that date
    final dayRecords = _controller.milkRecords.where((record) {
      final recordDate = record.date ?? DateTime.now();
      return recordDate.isAfter(startOfDay) && recordDate.isBefore(endOfDay);
    });

    final totalProduced = dayRecords.fold<double>(
      0.0, (sum, record) {
        final morning = record.morning ?? 0.0;
        final afternoon = record.afternoon ?? 0.0;
        final evening = record.evening ?? 0.0;
        return sum + morning + afternoon + evening;
      });

    // Calculate total sold and used on that date, excluding the specified sale if provided
    final daySales = _controller.unfilteredMilkSales.where((sale) {
      final saleDate = sale.date ?? DateTime.now();
      final isOnSameDate = saleDate.isAfter(startOfDay) && saleDate.isBefore(endOfDay);

      // Exclude the specified sale if provided (when editing)
      if (excludeSale != null && sale.id == excludeSale.id) {
        return false;
      }

      return isOnSameDate;
    });

    final totalSold = daySales.fold<double>(
      0.0, (sum, sale) => sum + (sale.quantity ?? 0.0));
    final totalHomeUsage = daySales.fold<double>(
      0.0, (sum, sale) => sum + (sale.homeUsage ?? 0.0));
    final totalCalfUsage = daySales.fold<double>(
      0.0, (sum, sale) => sum + (sale.calfUsage ?? 0.0));

    final availableMilk = totalProduced - totalSold - totalHomeUsage - totalCalfUsage;
    return availableMilk > 0 ? availableMilk : 0.0;
  }
}
